{"title": "MCP Settings", "description": "Manage and configure MCP (Model Context Protocol) servers and tools", "capability": {"title": "MCP Capabilities", "description": "Manage and configure MCP (Model Context Protocol) servers, tools and settings"}, "globalToggle": "Global Toggle", "serverCount": "{count} servers total", "status": {"enabled": "Enabled", "disabled": "Disabled"}, "disabled": {"title": "MCP Feature Disabled", "description": "Enable MCP feature to use server and tool calling functionality. MCP allows AI models to interact with external tools and services.", "enable": "Enable MCP"}, "enabled": "MCP enabled", "enabledDescription": "Enable or disable MCP functionality and tools.", "disabledDescription": "MCP feature is now disabled", "toggleError": "Failed to toggle MCP status", "refreshed": "Server list refreshed", "refreshError": "Failed to refresh server list", "serverAdded": "Server {name} added", "addServerError": "Failed to add server", "tools": {"searchPlaceholder": "Search tools...", "noToolsAvailable": "No tools available", "selectToolToDebug": "Select a tool to debug", "dialogDescription": "Debug and test tools provided by MCP servers", "toolsCount": "{count} tools", "availableTools": "Available Tools", "toolList": "Tool List", "functionDescription": "Function Description", "invalidJson": "Invalid JSON format", "inputHint": "Please enter parameters in JSON format", "required": "Required", "noDescription": "No description", "input": "Arguments", "path": "Path", "pathPlaceholder": "Enter file path", "searchPattern": "Search pattern", "searchPatternPlaceholder": "Enter regular expression", "filePattern": "File pattern", "filePatternPlaceholder": "Enter file pattern, e.g.: *.md", "executeButton": "Execute Tool", "resultTitle": "Result", "runningTool": "Executing tool...", "loading": "Loading...", "error": "Loading failed", "available": "{count} available", "none": "No tools available", "title": "MCP Tools", "description": "Tools provided by the MCP server", "loadError": "Failed to load tools", "parameters": "Parameters", "refresh": "Refresh", "disabled": "MCP is disabled", "enableToUse": "Please enable MCP to use tools", "enabled": "Enable MCP", "enabledDescription": "Enable MCP functionality to use tool calls", "empty": "Empty", "jsonInputPlaceholder": "Enter the parameters in JSON format", "type": "Type", "annotations": "Annotations", "invalidJsonFormat": "JSON format is incorrect"}, "addServer": "Add Server", "addServerDialog": {"description": "Configure a new MCP server", "title": "Add Server"}, "confirmDelete": {"cancel": "Cancel", "confirm": "Delete", "description": "Are you sure you want to delete the server {name}? \nThis operation cannot be cancelled.", "title": "Confirm deletion"}, "confirmRemoveServer": "Are you sure you want to delete the server {name}? \nThis operation cannot be cancelled.", "default": "default", "deleteServer": "Delete Server", "editServer": "Edit Server", "editServerDialog": {"description": "Edit MCP server configuration", "title": "Edit Server"}, "enableToAccess": "Please enable MCP to access configuration options.", "enabledTitle": "Enable MCP", "isDefault": "Default server", "noServersFound": "Server not found", "removeDefault": "Remove default", "removeServer": "Remove Server", "removeServerDialog": {"title": "Delete Server"}, "resetConfirm": "Rest<PERSON>", "resetConfirmDescription": "This action restores all default servers while retaining your customized servers. \nAny modifications to the default server will be lost.", "resetConfirmTitle": "Restore default service", "resetToDefault": "Restore default service", "running": "Running", "serverForm": {"add": "Add", "args": "Arguments", "argsPlaceholder": "Enter parameters, separated by spaces", "argsRequired": "Parameters cannot be empty", "autoApprove": "Auto-approve", "autoApproveAll": "All", "autoApproveHelp": "Select the operation type that requires automatic authorization and execute without user confirmation", "autoApproveRead": "Read", "autoApproveWrite": "Write", "baseUrl": "Base URL", "baseUrlPlaceholder": "Enter the server basic URL (for example: http://localhost:3000)", "cancel": "Cancel", "command": "Command", "commandPlaceholder": "Enter a command", "commandRequired": "The command cannot be empty", "configImported": "Configuration import succeeded", "description": "Description", "descriptionPlaceholder": "Enter the server description", "descriptions": "Description", "descriptionsPlaceholder": "Enter the server description", "env": "Environment Variables", "envInvalid": "Environment variables must be in valid JSON format", "envPlaceholder": "Enter environment variables in JSON format", "icon": "Icon", "iconPlaceholder": "Enter icon", "icons": "Icons", "iconsPlaceholder": "Enter icons", "jsonConfig": "JSON configuration", "jsonConfigExample": "JSON configuration example", "jsonConfigIntro": "You can directly paste the JSON configuration or choose to configure the server manually.", "jsonConfigPlaceholder": "Please paste the JSON format configuration of the MCP server", "name": "Server Name", "namePlaceholder": "Enter the server name", "nameRequired": "The server name cannot be empty", "parseAndContinue": "Parse and Continue", "parseError": "Parsing error", "parseSuccess": "Configuration parsed successfully", "skipToManual": "Skip to manual configuration", "submit": "Submit", "folders": "Folder List", "addFolder": "Add Folder", "selectFolder": "Select Folder", "selectFolderError": "Failed to select folder", "noFoldersSelected": "No folders selected", "type": "Server Type", "typeInMemory": "Memory", "typePlaceholder": "Select a server type", "typeSse": "Server-Sent Events (SSE)", "typeStdio": "Standard input and output", "update": "Update"}, "serverList": "Server list", "setAsDefault": "Set as the default server", "setDefault": "Set as default", "startServer": "Start the server", "stopServer": "Stop the server", "stopped": "Stopped", "tabs": {"servers": "Servers", "tools": "Tools"}, "inmemory": {"Artifacts": {"desc": "Produce richer Artifacts in QinCore", "name": "Artifacts"}, "bochaSearch": {"desc": "Bocha Search API https://open.bochaai.com/", "name": "Bocha Search"}, "buildInFileSystem": {"desc": "Allow QinCore to interact with the local file system.", "name": "File System"}, "mediaServer": {"desc": "Enable any model in QinCore to process images, videos, and audio files.", "name": "Media Service"}, "braveSearch": {"desc": "Brave Search API https://brave.com/search/api/", "name": "Brave Search"}, "powerpack": {"desc": "Provide models with time queries, web browsing, and secure code execution.", "name": "Power Pack"}, "difyKnowledge": {"desc": "Dify Knowledge Base Search Service, which can retrieve content in Dify Knowledge Base", "name": "Dify Knowledge Base Search"}, "ragflowKnowledge": {"name": "RAGFlow Knowledge Base Search", "desc": "RAGFlow knowledge base search service, can search RAGFlow knowledge base content"}, "fastGptKnowledge": {"name": "FastGPT Knowledge Base Search", "desc": "FastGPT knowledge base search service, can search FastGPT knowledge base content"}, "deepchat-inmemory/custom-prompts-server": {"desc": "QinCore built-in custom prompts service", "name": "Custom Prompts"}, "deepchat-inmemory/deep-research-server": {"desc": "QinCore built-in deep research powered by Bocha Search. Long-context models are recommended.", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "Auto Template Prompting", "desc": "Automatically select the best custom prompt based on input and fill the template intelligently."}, "deepchat-inmemory/conversation-search-server": {"name": "Conversation History Search", "desc": "QinCore built-in conversation history search for past chats and messages."}, "builtinKnowledge": {"desc": "QinCore built-in knowledge base search for QinCore docs and guides.", "name": "Built-in knowledge base search"}, "deepchat-inmemory/meeting-server": {"name": "Multi-Agent Meetings", "desc": "QinCore built-in meetings to host multi-agent discussions."}, "deepchat/apple-server": {"desc": "Let models operate macOS apps like Calendar, Contacts, Mail, Maps, Notes, and Reminders.", "name": "macOS System Assistant"}}, "prompts": {"noPromptsAvailable": "No Prompts Available", "noDescription": "No description yet", "selectPrompt": "Details for the selected prompt will be shown here.", "parameters": "Parameters", "input": "Parameters", "runningPrompt": "Fetching prompt...", "executeButton": "Get Prompt", "resultTitle": "Prompt Details", "invalidJson": "Invalid JSON format", "parametersHint": "Please enter the parameters in JSON format, support automatic formatting", "resetToDefault": "Reset to default parameters", "dialogDescription": "Debug and test prompts provided by MCP servers"}, "resources": {"noResourcesAvailable": "No Resources Available", "selectResource": "Select a resource to view its content.", "loading": "Loading...", "loadContent": "Load Content", "pleaseSelect": "Click to view resource details.", "dialogDescription": "Browse and view resources provided by MCP servers"}, "errors": {"loadConfigFailed": "Failed to load MCP configuration", "setEnabledFailed": "Failed to set MCP enabled state", "getServerStatusFailed": "Failed to get status for server {serverName}", "addServerFailed": "Failed to add server", "updateServerFailed": "Failed to update server", "removeServerFailed": "Failed to remove server", "maxDefaultServersReached": "Maximum number of default servers (30) reached", "toggleDefaultServerFailed": "Failed to toggle default server status", "resetToDefaultFailed": "Failed to reset to default servers", "toggleServerFailed": "Failed to toggle server {serverName}", "loadToolsFailed": "Failed to load tools", "loadPromptsFailed": "Failed to load prompts", "loadResourcesFailed": "Failed to load resources", "callToolFailed": "Failed to call tool {toolName}", "toolCallError": "Tool call error: {error}", "mcpDisabled": "MCP is disabled", "getPromptFailed": "Failed to get prompt", "readResourceFailed": "Failed to read resource"}, "market": {"browseBuiltin": "Browse Built-in MCP Market", "builtinTitle": "MCP Market", "poweredBy": "Powered by <PERSON><PERSON><PERSON><PERSON>", "keyGuide": "Get API Key", "keyHelpText": "Please go to", "keyHelpEnd": "to apply for an API Key and fill it in the input box above", "apiKeyPlaceholder": "Enter MCPRouter API Key", "apiKeyRequiredTitle": "API Key Required", "apiKeyRequiredDesc": "Please fill in the MCPRouter API Key before installation", "install": "Install", "installed": "Installed", "installSuccess": "Installation successful", "installFailed": "Installation failed", "noMore": "No more", "empty": "No services", "loadMore": "Load More", "pullDownToLoad": "Continue pulling down to load more", "externalMarkets": "External Markets", "externalMarketsDesc": "Browse third-party MCP server markets to discover more available servers and tools"}}