appId: com.dilu.daqinzhongqi
productName: QinCore
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!test/*'
  - '!docs/*'
  - '!keys/*'
  - '!scripts/*'
  - '!.github/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!electron-builder.yml'
  - '!electron-builder-macx64.yml'
  - '!test/*'
  - '!*.config.ts'
  - '!*.config.js'
  - '!**/{LICENSE,LICENSE.txt,*.LICENSE.txt,NOTICE.txt,README.md,CHANGELOG.md,CONTRIBUTING.md,CONTRIBUTING.zh.md,README.zh.md,README.jp.md}'
  - '!**/{.DS_Store,Thumbs.db}'
  - '!*.md'

asarUnpack:
  - '**/node_modules/sharp/**/*'
  - '**/node_modules/@img/**/*'
extraResources:
  - from: ./runtime/
    to: app.asar.unpacked/runtime
    filter: ['**/*']
electronLanguages:
  - zh-CN
  - zh-TW
  - zh-HK
  - en-US
  - ja-JP
  - ko-KR
  - fr-FR
  - ru-RU
  - ja
  - ru
  - zh_CN
  - zh_TW
  - zh_HK
  - en
  - ko
  - fr
  - fa-IR
  - fa
win:
  executableName: QinCore
nsis:
  artifactName: ${name}-${version}-windows-${arch}.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  allowToChangeInstallationDirectory: true
  oneClick: false
  include: build/nsis-installer.nsh
afterSign: scripts/notarize.js
afterPack: scripts/afterPack.js
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  gatekeeperAssess: false
  category: public.app-category.utilities
  target:
    - target: dmg
      arch: arm64
    - target: zip
      arch: arm64
  artifactName: ${name}-${version}-mac-${arch}.${ext}
linux:
  target:
    - AppImage
    - tar.gz
  maintainer: Dilu
  category: Utility
  artifactName: ${name}-${version}-linux-${arch}.${ext}
  mimeTypes:
    - x-scheme-handler/deepchat
npmRebuild: false
publish:
  provider: generic
  url: https://cdn.deepchatai.cn/upgrade/
