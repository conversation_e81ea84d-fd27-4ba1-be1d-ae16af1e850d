{"title": "显卡信息", "loading": "正在加载显卡信息...", "error": "获取显卡信息失败", "retry": "重试", "gpu": "显卡", "memory": "显存", "gpuUtilization": "GPU负载", "cpuUtilization": "CPU负载", "memoryUtilization": "内存使用率", "utilization": "使用率", "temperature": "温度", "fanSpeed": "风扇转速", "power": "功耗", "powerUtilization": "功耗使用率", "details": "详细信息", "driverVersion": "驱动版本", "pcie": "PCIe连接", "clock": "核心频率", "memoryClock": "显存频率", "processes": "进程数", "refresh": "刷新", "notSupported": "未检测到NVIDIA显卡或未安装NVIDIA驱动程序", "gpuUsage": "显卡使用情况", "cpuAndMemoryUsage": "CPU和内存使用情况", "diskUsage": "硬盘使用情况", "diskPerformance": "硬盘负载", "performance": "性能", "highMemoryUsageWarning": "显存占用已达{threshold}%！随时可能爆显存，建议升级显卡。", "highGpuUtilizationWarning": "GPU负载已达100%！GPU正在满负荷运行。", "highTemperatureWarning": "GPU温度已达{threshold}°C！温度过高，建议打开机箱或加装散热器散热。", "highPowerUsageWarning": "功耗已达99.7%！正在满负荷运行，注意电源供电。", "highCpuUsageWarning": "CPU负载已达100%！CPU正在满负荷运行，建议更换下一代CPU。", "highDiskPerformanceWarning": "硬盘读取性能已达{threshold}%！影响项目解压和安装速度。", "highSystemMemoryUsageWarning": "内存使用率已达{threshold}%！内存正在满负荷运行，建议加装内存。"}