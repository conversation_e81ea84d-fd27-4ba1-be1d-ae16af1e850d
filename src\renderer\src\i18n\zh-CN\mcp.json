{"title": "MCP管理", "description": "管理和配置MCP（Model Context Protocol）服务器和工具", "capability": {"title": "MCP能力", "description": "管理和配置MCP（Model Context Protocol）服务器、工具和设置"}, "globalToggle": "全局开关", "serverCount": "共{count}个服务器", "status": {"enabled": "已启用", "disabled": "已禁用"}, "disabled": {"title": "MCP功能已禁用", "description": "启用MCP功能以使用服务器和工具调用功能。MCP允许AI模型与外部工具和服务进行交互。", "enable": "启用MCP"}, "enabled": "MCP已启用", "enabledDescription": "启用或禁用MCP功能和工具", "disabledDescription": "MCP功能现在已禁用", "toggleError": "切换MCP状态失败", "refreshed": "服务器列表已刷新", "refreshError": "刷新服务器列表失败", "serverAdded": "服务器 {name} 已添加", "addServerError": "添加服务器失败", "enabledTitle": "启用MCP", "enableToAccess": "请先启用MCP以访问配置选项", "errors": {"loadConfigFailed": "加载MCP配置失败", "setEnabledFailed": "设置MCP启用状态失败", "getServerStatusFailed": "获取服务器 {serverName} 状态失败", "addServerFailed": "添加服务器失败", "updateServerFailed": "更新服务器失败", "removeServerFailed": "删除服务器失败", "maxDefaultServersReached": "最多只能设置30个默认服务器", "toggleDefaultServerFailed": "切换默认服务器状态失败", "resetToDefaultFailed": "恢复默认服务器失败", "toggleServerFailed": "切换服务器 {serverName} 状态失败", "loadToolsFailed": "加载工具失败", "loadPromptsFailed": "加载提示模板失败", "loadResourcesFailed": "加载资源失败", "callToolFailed": "调用工具 {toolName} 失败", "toolCallError": "工具调用错误: {error}", "mcpDisabled": "MCP功能已禁用", "getPromptFailed": "获取提示模板失败", "readResourceFailed": "读取资源失败"}, "tabs": {"servers": "服务器", "tools": "工具"}, "serverList": "服务器列表", "addServer": "添加服务器", "running": "运行中", "stopped": "已停止", "stopServer": "停止服务器", "startServer": "启动服务器", "noServersFound": "未找到服务器", "addServerDialog": {"title": "添加服务器", "description": "配置新的MCP服务器"}, "editServerDialog": {"title": "编辑服务器", "description": "编辑MCP服务器配置"}, "serverForm": {"name": "服务器名称", "namePlaceholder": "输入服务器名称", "nameRequired": "服务器名称不能为空", "type": "服务器类型", "typePlaceholder": "选择服务器类型", "typeStdio": "标准输入输出", "typeSse": "服务器发送事件", "typeInMemory": "内存", "baseUrl": "基础URL", "baseUrlPlaceholder": "输入服务器基础URL（如：http://localhost:3000）", "command": "命令", "commandPlaceholder": "输入命令", "commandRequired": "命令不能为空", "args": "参数", "argsPlaceholder": "输入参数，用空格分隔", "argsRequired": "参数不能为空", "env": "环境变量", "envPlaceholder": "输入JSON格式的环境变量", "envInvalid": "环境变量必须是有效的JSON格式", "description": "描述", "descriptionPlaceholder": "输入服务器描述", "descriptions": "描述", "descriptionsPlaceholder": "输入服务器描述", "icon": "图标", "iconPlaceholder": "输入图标", "icons": "图标", "iconsPlaceholder": "输入图标", "autoApprove": "自动授权", "autoApproveAll": "全部", "autoApproveRead": "读取", "autoApproveWrite": "写入", "autoApproveHelp": "选择需要自动授权的操作类型，无需用户确认即可执行", "submit": "提交", "add": "添加", "update": "更新", "cancel": "取消", "jsonConfigIntro": "您可以直接粘贴JSON配置或选择手动配置服务器。", "jsonConfig": "JSON配置", "jsonConfigPlaceholder": "请粘贴MCP服务器的JSON格式配置", "jsonConfigExample": "JSON配置示例", "parseSuccess": "配置解析成功", "configImported": "配置导入成功", "parseError": "解析错误", "skipToManual": "跳过至手动配置", "parseAndContinue": "解析并继续", "folders": "文件夹列表", "addFolder": "添加文件夹", "selectFolder": "选择文件夹", "selectFolderError": "选择文件夹失败", "noFoldersSelected": "未选择任何文件夹"}, "deleteServer": "删除服务器", "editServer": "编辑服务器", "setDefault": "设为默认", "removeDefault": "移除默认", "isDefault": "默认服务器", "default": "默认", "setAsDefault": "设为默认服务器", "removeServer": "删除服务器", "confirmRemoveServer": "确定要删除服务器 {name} 吗？此操作无法撤销。", "removeServerDialog": {"title": "删除服务器"}, "confirmDelete": {"title": "确认删除", "description": "确定要删除服务器 {name} 吗？此操作无法撤销。", "confirm": "删除", "cancel": "取消"}, "resetToDefault": "恢复默认服务", "resetConfirmTitle": "恢复默认服务", "resetConfirmDescription": "此操作将恢复所有默认服务器，同时保留您自定义的服务器。对默认服务器的任何修改将会丢失。", "resetConfirm": "恢复", "tools": {"searchPlaceholder": "搜索工具...", "noToolsAvailable": "暂无可用工具", "selectToolToDebug": "选择要调试的工具", "dialogDescription": "调试和测试MCP服务器提供的工具", "toolsCount": "个工具", "availableTools": "可用工具", "toolList": "工具列表", "functionDescription": "功能描述", "invalidJson": "无效的JSON格式", "inputHint": "请输入JSON格式的参数", "required": "必需", "noDescription": "暂无描述", "input": "输入参数", "path": "路径", "pathPlaceholder": "输入文件路径", "searchPattern": "搜索模式", "searchPatternPlaceholder": "输入正则表达式", "filePattern": "文件模式", "filePatternPlaceholder": "输入文件模式，例如: *.md", "executeButton": "执行工具", "resultTitle": "执行结果", "runningTool": "正在执行工具", "loading": "加载中...", "error": "加载失败", "available": "{count}个工具可用", "none": "没有可用工具", "title": "MCP工具", "description": "MCP服务器提供的工具", "loadError": "加载工具失败", "parameters": "参数", "refresh": "刷新", "disabled": "MCP已禁用", "enableToUse": "请先启用MCP以使用工具", "enabled": "启用MCP", "enabledDescription": "启用MCP功能以使用工具调用", "jsonInputPlaceholder": "输入JSON格式的参数", "type": "类型", "annotations": "注解", "empty": "空", "invalidJsonFormat": "JSON格式不正确"}, "inmemory": {"bochaSearch": {"name": "博查搜索", "desc": "博查搜索 API https://open.bochaai.com/"}, "buildInFileSystem": {"name": "文件系统", "desc": "让 大秦重器 能够操作本地文件"}, "Artifacts": {"name": "Artifacts", "desc": "让你的 大秦重器 输出的多样化的 Artifacts 内容"}, "mediaServer": {"name": "媒体服务", "desc": "开启后 大秦重器 中任意模型都能处理图片、视频、音频等媒体文件"}, "braveSearch": {"name": "Brave搜索", "desc": "Brave搜索 API https://brave.com/search/api/"}, "powerpack": {"name": "增强工具包", "desc": "为任意大模型提供时间查询、网页信息获取和安全的代码执行等增强能力，让模型拥有更强大和准确的信息获取能力"}, "difyKnowledge": {"name": "Dify知识库检索", "desc": "Dify知识库检索服务，可以检索Dify知识库中的内容"}, "ragflowKnowledge": {"name": "RAGFlow知识库检索", "desc": "RAGFlow知识库检索服务，可以对RAGFlow知识库内容进行检索"}, "fastGptKnowledge": {"name": "FastGPT知识库检索", "desc": "FastGPT知识库检索服务，可以对FastGPT知识库内容进行检索"}, "builtinKnowledge": {"name": "内置知识库检索", "desc": "大秦重器内置知识库检索服务，可以对大秦重器内置知识库内容进行检索"}, "deepchat-inmemory/custom-prompts-server": {"name": "自定义提示词", "desc": "大秦重器内置自定义提示词服务"}, "deepchat-inmemory/deep-research-server": {"name": "DeepResearch", "desc": "基于博查搜索的大秦重器内置深度研究服务，（注意需要长上下文模型才能使用，上下文不足的模型可能会失败）"}, "deepchat-inmemory/auto-prompting-server": {"name": "自动模板提示词", "desc": "根据用户输入自动选择最合适的自定义提示词，并智能填充提示词模板"}, "deepchat-inmemory/conversation-search-server": {"name": "对话历史搜索", "desc": "大秦重器内置对话历史搜索服务，可搜索历史对话记录和消息内容"}, "deepchat-inmemory/meeting-server": {"name": "多智能体会议", "desc": "大秦重器 内置会议服务，支持发起和主持多智能体讨论"}, "deepchat-inmemory/media-server": {"name": "媒体处理服务", "desc": "大秦重器 内置媒体处理服务，支持图片、视频、音频文件的上传和处理"}, "deepchat/apple-server": {"name": "macOS系统助手", "desc": "让模型能操作macOS的日历、联系人、邮件、地图、备忘录、提醒事项等系统功能"}}, "prompts": {"noPromptsAvailable": "暂无可用的 Prompts", "noDescription": "暂无描述", "selectPrompt": "此处展示选中的Prompt", "parameters": "Prompt 参数", "input": "输入参数", "runningPrompt": "Prompt 获取中", "executeButton": "点击获取", "resultTitle": "Prompt 详情", "resetToDefault": "重置为默认参数", "invalidJson": "无效的 JSON 格式", "parametersHint": "请输入 JSON 格式的参数，支持自动格式化", "dialogDescription": "调试和测试MCP服务器提供的提示模板"}, "resources": {"noResourcesAvailable": "暂无 Resources", "selectResource": "此处展示 Resources 内容", "loading": "加载中", "loadContent": "获取 Resource 内容", "pleaseSelect": "点击获取展示Resources详情", "dialogDescription": "浏览和查看MCP服务器提供的资源"}, "market": {"browseBuiltin": "浏览内置 MCP 市场", "builtinTitle": "MCP 市场", "poweredBy": "Powered by <PERSON><PERSON><PERSON><PERSON>", "keyGuide": "获取密钥", "keyHelpText": "请先到", "keyHelpEnd": "申请 API Key 后填写到上方输入框", "apiKeyPlaceholder": "输入 MCPRouter API Key", "apiKeyRequiredTitle": "需要 API Key", "apiKeyRequiredDesc": "请先填写 MCPRouter API Key 后再安装", "install": "安装", "installed": "已安装", "installSuccess": "安装成功", "installFailed": "安装失败", "noMore": "没有更多了", "empty": "暂无服务", "loadMore": "加载更多", "pullDownToLoad": "继续下拉加载更多", "externalMarkets": "外部市场", "externalMarketsDesc": "浏览第三方MCP服务器市场，发现更多可用的服务器和工具"}}