{"name": "qincore", "version": "0.3.1", "description": "QinCore，一个强大的AI重器。", "main": "./out/main/index.js", "author": "<PERSON><PERSON>", "type": "module", "engines": {"node": ">=20.19.0", "pnpm": ">=10.11.0"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "scripts": {"preinstall": "npx only-allow pnpm", "test": "vitest", "test:main": "vitest --config vitest.config.ts test/main", "test:renderer": "vitest --config vitest.config.renderer.ts test/renderer", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "format:check": "prettier --check .", "format": "prettier --write .", "lint": "npx -y oxlint .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "pnpm run typecheck:node && pnpm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev --watch", "dev:inspect": "electron-vite dev --watch --inspect=9229", "dev:linux": "electron-vite dev --watch --noSandbox", "build": "pnpm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps && simple-git-hooks", "build:unpack": "pnpm run build && electron-builder --dir", "build:win": "pnpm run build && electron-builder --win", "build:win:x64": "pnpm run build && electron-builder --win --x64", "build:win:arm64": "pnpm run build && electron-builder --win --arm64", "install:sharp": "node scripts/install-sharp-for-platform.js", "build:mac": "pnpm run build && electron-builder --mac", "build:mac:arm64": "pnpm run build && electron-builder --mac --arm64", "build:mac:x64": "pnpm run build && electron-builder -c electron-builder-macx64.yml --mac --x64 ", "build:linux": "pnpm run build && electron-builder --linux", "build:linux:x64": "pnpm run build && electron-builder --linux --x64", "build:linux:arm64": "pnpm run build && electron-builder --linux --arm64", "afterSign": "scripts/notarize.js", "installRuntime": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 && npx -y tiny-runtime-injector --type bun --dir ./runtime/bun --runtime-version v1.2.20", "installRuntime:win:x64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a x64 -p win32 && npx -y tiny-runtime-injector --type node --dir ./runtime/node -a x64 -p win32", "installRuntime:win:arm64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a arm64 -p win32 && npx -y tiny-runtime-injector --type node --dir ./runtime/node -a arm64 -p win32", "installRuntime:mac:arm64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a arm64 -p darwin && npx -y tiny-runtime-injector --type bun --runtime-version v1.2.20 --dir ./runtime/bun -a arm64 -p darwin", "installRuntime:mac:x64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a x64 -p darwin && npx -y tiny-runtime-injector --type bun --runtime-version v1.2.20 --dir ./runtime/bun -a x64 -p darwin", "installRuntime:linux:x64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a x64 -p linux && npx -y tiny-runtime-injector --type bun --runtime-version v1.2.20 --dir ./runtime/bun -a x64 -p linux", "installRuntime:linux:arm64": "npx -y tiny-runtime-injector --type uv --dir ./runtime/uv --runtime-version 0.8.8 -a arm64 -p linux && npx -y tiny-runtime-injector --type bun --runtime-version v1.2.20 --dir ./runtime/bun -a arm64 -p linux", "installRuntime:duckdb:vss": "node scripts/installVss.js", "i18n": "i18n-check -s zh-CN -f i18next --locales src/renderer/src/i18n", "i18n:en": "i18n-check -s en-US -f i18next --locales src/renderer/src/i18n", "i18n:types": "node scripts/generate-i18n-types.js", "cleanRuntime": "rm -rf runtime/uv runtime/bun runtime/node"}, "dependencies": {"@anthropic-ai/sdk": "^0.53.0", "@aws-sdk/client-bedrock": "^3.840.0", "@aws-sdk/client-bedrock-runtime": "^3.842.0", "@duckdb/node-api": "1.3.2-alpha.25", "@e2b/code-interpreter": "^1.5.1", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@google/genai": "^1.13.0", "@jxa/run": "^1.4.0", "@modelcontextprotocol/sdk": "^1.17.2", "axios": "^1.7.9", "better-sqlite3-multiple-ciphers": "12.2.0", "cheerio": "^1.0.0", "compare-versions": "^6.1.1", "dayjs": "^1.11.13", "diff": "^7.0.0", "electron-log": "^5.3.3", "electron-store": "^8.2.0", "electron-updater": "^6.1.7", "electron-window-state": "^5.0.3", "fflate": "^0.8.2", "file-type": "^20.5.0", "glob": "^11.0.3", "https-proxy-agent": "^7.0.6", "jsonrepair": "^3.13.0", "mammoth": "^1.9.0", "mime-types": "^2.1.35", "nanoid": "^5.1.5", "ollama": "^0.5.16", "openai": "^5.12.2", "pdf-parse-new": "^1.3.9", "run-applescript": "^7.0.0", "sharp": "^0.33.5", "together-ai": "^0.16.0", "tokenx": "^0.4.1", "turndown": "^7.2.0", "undici": "^7.8.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xml2js": "^0.6.2", "zod": "^3.24.3"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^3.0.1", "@electron/rebuild": "^4.0.1", "@iconify-json/lucide": "^1.2.39", "@iconify-json/vscode-icons": "^1.2.20", "@iconify/vue": "^5.0.0", "@lingual/i18n-check": "^0.8.4", "@radix-icons/vue": "^1.0.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tiptap/core": "^2.11.7", "@tiptap/extension-code-block": "^2.11.9", "@tiptap/extension-document": "^2.11.7", "@tiptap/extension-hard-break": "^2.11.7", "@tiptap/extension-history": "^2.12.0", "@tiptap/extension-mention": "^2.11.7", "@tiptap/extension-paragraph": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/suggestion": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@types/better-sqlite3": "^7.6.0", "@types/mime-types": "^3.0.1", "@types/node": "^22.14.1", "@types/xlsx": "^0.0.35", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "@vueuse/core": "^12.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "electron": "^37.3.0", "electron-builder": "26.0.12", "electron-vite": "^4.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.5", "lucide-vue-next": "^0.511.0", "mermaid": "^11.6.0", "minimatch": "^10.0.1", "monaco-editor": "^0.52.2", "picocolors": "^1.1.1", "pinia": "^3.0.2", "prettier": "^3.5.3", "radix-vue": "^1.9.14", "simple-git-hooks": "^2.13.0", "tailwind-merge": "^3.3.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "typescript": "^5.8.3", "vite": "7.1.1", "vite-plugin-monaco-editor-esm": "^2.0.2", "vite-plugin-vue-devtools": "^8.0.0", "vite-svg-loader": "^5.1.0", "vitest": "^3.2.4", "vue": "^3.5.19", "vue-i18n": "^11.1.11", "vue-renderer-markdown": "^0.0.34", "vue-router": "4", "vue-tsc": "^2.2.10", "vue-use-monaco": "^0.0.8", "vue-virtual-scroller": "^2.0.0-beta.8", "vuedraggable": "^4.1.0", "yaml": "^2.8.0", "zod-to-json-schema": "^3.24.6"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged && pnpm typecheck", "commit-msg": "node scripts/verify-commit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts": ["prettier --parser=typescript --write"]}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "electron", "electron-winstaller", "lzo", "simple-git-hooks"], "ignoredBuiltDependencies": ["better-sqlite3-multiple-ciphers", "esbuild", "maplibre-gl", "sharp", "vue-demi"]}}