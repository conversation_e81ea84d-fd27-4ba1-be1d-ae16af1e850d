import common from './common.json'
import update from './update.json'
import routes from './routes.json'
import chat from './chat.json'
import model from './model.json'
import thread from './thread.json'
import dialog from './dialog.json'
import settings from './settings.json'
import mcp from './mcp.json'
import welcome from './welcome.json'
import artifacts from './artifacts.json'
import sync from './sync.json'
import toolCall from './toolCall.json'
import components from './components.json'
import newThread from './newThread.json'
import about from './about.json'
import contextMenu from './contextMenu.json'
import promptSetting from './promptSetting.json'
import appbar from './appbar.json'
import gpuInfo from './gpuInfo.json'

// Individual top-level keys
const others = {
  Silicon: 'SiliconFlow',
  Qiniu: 'Qiniu',
  QwenLM: 'Qwen Model',
  Doubao: 'Doubao',
  PPIO: 'PPIO Cloud',
  Moonshot: 'Moonshot AI',
  DashScope: '<PERSON><PERSON><PERSON>',
  <PERSON><PERSON><PERSON>: 'Hunyuan',
  searchDisclaimer:
    'QinCore is only an auxiliary tool that organizes and summarizes public data returned by search engines when users actively initiate searches, helping users view and understand search results more conveniently.\n1. Use of Public Data\nThis software only processes data that is publicly available on target websites or search engines without requiring login. Before using, please be sure to review and comply with the terms of service of the target website or search engine to ensure your usage is legal and compliant.\n2. Information Accuracy and Responsibility\nThe content organized and generated by this software is for reference only and does not constitute any form of legal, business, or other advice. The developers make no guarantees regarding the accuracy, completeness, timeliness, or legality of search results, and any consequences arising from the use of this software are solely the responsibility of the user.\n3. Disclaimer Clause\nThis software is provided "as is," and the developers do not assume any express or implied warranty or responsibility for its performance, stability, or applicability. In the process of using this software, the developers assume no responsibility for any disputes, losses, or legal liabilities arising from violations of relevant laws and regulations or the rules of the target website.\n4. User Self-Discipline\nBefore using this software, users should fully understand and confirm that their use will not infringe upon the intellectual property rights, trade secrets, or other legitimate rights of others. Any legal disputes and consequences arising from improper use of this software by users are the sole responsibility of the users.\nUse of this software indicates that the user has read, understood, and agreed to all terms of this disclaimer. If you have any questions, please consult a professional legal advisor.'
}

export default {
  common,
  update,
  routes,
  chat,
  model,
  thread,
  dialog,
  settings,
  mcp,
  welcome,
  artifacts,
  sync,
  toolCall,
  components,
  newThread,
  about,
  contextMenu,
  promptSetting,
  appbar,
  gpuInfo,
  ...others
}
